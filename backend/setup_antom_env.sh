#!/bin/bash

# Antom 环境配置脚本
# 用于快速设置 Antom 支付环境

echo "=== Antom 支付环境配置脚本 ==="
echo ""

# 检查是否存在 secrets.env 文件
if [ ! -f "secrets.env" ]; then
    echo "📋 复制配置文件模板..."
    if [ -f "secrets.env.example" ]; then
        cp secrets.env.example secrets.env
        echo "✅ 已创建 secrets.env 文件"
    else
        echo "❌ 找不到 secrets.env.example 文件"
        exit 1
    fi
else
    echo "📋 发现现有的 secrets.env 文件"
fi

echo ""
echo "🔧 请选择要配置的环境:"
echo "1) 沙盒环境 (Sandbox) - 用于开发测试"
echo "2) 生产环境 (Production) - 用于正式运营"
echo "3) 查看当前配置"
echo "4) 退出"
echo ""

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🏗️  配置沙盒环境..."
        
        # 设置环境为沙盒
        if grep -q "ANTOM_ENVIRONMENT=" secrets.env; then
            sed -i 's/ANTOM_ENVIRONMENT=.*/ANTOM_ENVIRONMENT=sandbox/' secrets.env
        else
            echo "ANTOM_ENVIRONMENT=sandbox" >> secrets.env
        fi
        
        echo "✅ 已设置为沙盒环境"
        echo ""
        echo "📝 请在 secrets.env 文件中配置以下沙盒环境变量:"
        echo "   - ANTOM_SANDBOX_CLIENT_ID"
        echo "   - ANTOM_SANDBOX_PUBLIC_KEY"
        echo "   - ANTOM_SANDBOX_MERCHANT_PRIVATE_KEY"
        echo "   - ANTOM_SANDBOX_NOTIFY_URL"
        echo "   - ANTOM_SANDBOX_REDIRECT_URL"
        ;;
        
    2)
        echo ""
        echo "🚀 配置生产环境..."
        
        # 设置环境为生产
        if grep -q "ANTOM_ENVIRONMENT=" secrets.env; then
            sed -i 's/ANTOM_ENVIRONMENT=.*/ANTOM_ENVIRONMENT=production/' secrets.env
        else
            echo "ANTOM_ENVIRONMENT=production" >> secrets.env
        fi
        
        echo "✅ 已设置为生产环境"
        echo ""
        echo "⚠️  重要提醒:"
        echo "   - 生产环境将处理真实的支付交易"
        echo "   - 请确保所有URL使用HTTPS"
        echo "   - 妥善保管生产环境的密钥"
        echo ""
        echo "📝 请在 secrets.env 文件中配置以下生产环境变量:"
        echo "   - ANTOM_PRODUCTION_CLIENT_ID"
        echo "   - ANTOM_PRODUCTION_PUBLIC_KEY"
        echo "   - ANTOM_PRODUCTION_MERCHANT_PRIVATE_KEY"
        echo "   - ANTOM_PRODUCTION_NOTIFY_URL"
        echo "   - ANTOM_PRODUCTION_REDIRECT_URL"
        ;;
        
    3)
        echo ""
        echo "📊 检查当前配置..."
        
        # 检查虚拟环境
        if [ -f "./venv/bin/python" ]; then
            ./venv/bin/python manage.py check_antom_config
        elif command -v python &> /dev/null; then
            python manage.py check_antom_config
        else
            echo "❌ 找不到 Python 环境"
            exit 1
        fi
        ;;
        
    4)
        echo "👋 退出配置"
        exit 0
        ;;
        
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🔍 验证配置..."

# 运行配置检查
if [ -f "./venv/bin/python" ]; then
    ./venv/bin/python manage.py check_antom_config
elif command -v python &> /dev/null; then
    python manage.py check_antom_config
else
    echo "❌ 找不到 Python 环境，请手动运行: python manage.py check_antom_config"
fi

echo ""
echo "📚 更多信息请查看:"
echo "   - docs/ANTOM_ENVIRONMENT_SETUP.md"
echo "   - PAYMENT_IMPLEMENTATION_SUMMARY.md"
echo ""
echo "🎉 配置完成！"
