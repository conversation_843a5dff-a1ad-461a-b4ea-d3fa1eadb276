from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal
import uuid


# 支付创建相关模式
class PaymentCreateRequest(BaseModel):
    """创建支付请求"""
    amount: float = Field(..., gt=0, description="支付金额")
    currency: str = Field(default="USD", description="货币类型")
    membership_tier: str = Field(..., description="会员等级")
    
    @validator('currency')
    def validate_currency(cls, v):
        allowed_currencies = ['USD', 'CNY', 'EUR', 'GBP']
        if v not in allowed_currencies:
            raise ValueError(f'Currency must be one of {allowed_currencies}')
        return v
    
    @validator('membership_tier')
    def validate_membership_tier(cls, v):
        allowed_tiers = ['free', 'premium', 'plus']
        if v not in allowed_tiers:
            raise ValueError(f'Membership tier must be one of {allowed_tiers}')
        return v


class PaymentCreateResponse(BaseModel):
    """创建支付响应"""
    payment_id: str = Field(..., description="本地支付记录ID")
    amount: float = Field(..., description="支付金额")
    currency: str = Field(..., description="货币类型")
    membership_tier: str = Field(..., description="会员等级")
    status: str = Field(..., description="支付状态")
    created_at: datetime = Field(..., description="创建时间")


# Antom 会话创建相关模式
class AntomSessionRequest(BaseModel):
    """Antom会话创建请求"""
    payment_id: str = Field(..., description="本地支付记录ID")
    
    @validator('payment_id')
    def validate_payment_id(cls, v):
        try:
            uuid.UUID(v)
        except ValueError:
            raise ValueError('Invalid payment ID format')
        return v


class AntomSessionResponse(BaseModel):
    """Antom会话创建响应"""
    success: bool = Field(..., description="是否成功")
    payment_id: str = Field(..., description="本地支付记录ID")
    antom_payment_request_id: str = Field(..., description="Antom支付请求ID")
    session_data: Dict[str, Any] = Field(..., description="Antom会话数据")
    redirect_url: Optional[str] = Field(None, description="支付页面重定向URL")


# 支付状态查询相关模式
class PaymentStatusRequest(BaseModel):
    """支付状态查询请求"""
    payment_id: str = Field(..., description="支付记录ID")
    
    @validator('payment_id')
    def validate_payment_id(cls, v):
        try:
            uuid.UUID(v)
        except ValueError:
            raise ValueError('Invalid payment ID format')
        return v


class PaymentStatusResponse(BaseModel):
    """支付状态响应"""
    payment_id: str = Field(..., description="支付记录ID")
    status: str = Field(..., description="支付状态")
    amount: float = Field(..., description="支付金额")
    currency: str = Field(..., description="货币类型")
    membership_tier: str = Field(..., description="会员等级")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    paid_at: Optional[datetime] = Field(None, description="支付时间")
    antom_payment_request_id: Optional[str] = Field(None, description="Antom支付请求ID")


# 支付历史相关模式
class PaymentHistoryResponse(BaseModel):
    """支付历史响应"""
    payments: List[PaymentStatusResponse] = Field(..., description="支付记录列表")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")


# Antom 回调相关模式
class AntomCallbackRequest(BaseModel):
    """Antom回调请求"""
    notify_type: str = Field(..., description="通知类型")
    payment_request_id: str = Field(..., description="支付请求ID")
    result_code: str = Field(..., description="结果代码")
    result_status: str = Field(..., description="结果状态")
    result_message: str = Field(..., description="结果消息")
    payment_amount: Optional[Dict[str, Any]] = Field(None, description="支付金额信息")
    payment_time: Optional[str] = Field(None, description="支付时间")
    
    class Config:
        extra = "allow"  # 允许额外字段


class AntomCallbackResponse(BaseModel):
    """Antom回调响应"""
    result_code: str = Field(..., description="处理结果代码")
    result_message: str = Field(..., description="处理结果消息")
    result_status: str = Field(..., description="处理结果状态")


# 订阅更新相关模式
class SubscriptionUpdateRequest(BaseModel):
    """订阅更新请求"""
    payment_id: str = Field(..., description="支付记录ID")
    
    @validator('payment_id')
    def validate_payment_id(cls, v):
        try:
            uuid.UUID(v)
        except ValueError:
            raise ValueError('Invalid payment ID format')
        return v


class SubscriptionUpdateResponse(BaseModel):
    """订阅更新响应"""
    success: bool = Field(..., description="是否成功")
    user_id: int = Field(..., description="用户ID")
    subscription_type: str = Field(..., description="订阅类型")
    start_date: datetime = Field(..., description="开始时间")
    end_date: Optional[datetime] = Field(None, description="结束时间")
    is_active: bool = Field(..., description="是否激活")


# 错误响应模式
class ErrorResponse(BaseModel):
    """错误响应"""
    error: str = Field(..., description="错误信息")
    code: Optional[str] = Field(None, description="错误代码")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")


# 通用响应模式
class SuccessResponse(BaseModel):
    """成功响应"""
    success: bool = Field(True, description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


# 支付查询请求（用于现有的inquiry接口）
class PaymentInquiryRequest(BaseModel):
    """支付查询请求"""
    payment_request_id: str = Field(..., description="Antom支付请求ID")


# 支付会话请求（用于现有的session接口）
class PaymentSessionRequest(BaseModel):
    """支付会话请求"""
    amount_value: float = Field(..., alias="amountValue", description="支付金额")
    currency: str = Field(..., description="货币类型")


# 支付响应（用于现有接口）
class PaymentResponse(BaseModel):
    """支付响应"""
    success: bool = Field(..., description="是否成功")
    payment_request_id: str = Field(..., description="支付请求ID")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    error: Optional[str] = Field(None, description="错误信息")


# 通知结果模式
class NotifyResult(BaseModel):
    """通知结果"""
    result_code: str = Field(..., alias="resultCode", description="结果代码")
    result_message: str = Field(..., alias="resultMessage", description="结果消息")
    result_status: str = Field(..., alias="resultStatus", description="结果状态")


class NotifyResponse(BaseModel):
    """通知响应"""
    result: NotifyResult = Field(..., description="通知结果")
