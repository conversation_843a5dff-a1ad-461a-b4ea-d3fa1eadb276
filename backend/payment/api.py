from __future__ import annotations
import json
import time
import uuid
from ninja import Schema
from ninja_extra import api_controller, route
from ninja.errors import HttpError
from django.http import HttpRequest
from typing import Dict, Any

from com.alipay.ams.api.default_alipay_client import DefaultAlipayClient
from com.alipay.ams.api.exception.exception import AlipayApiException
from com.alipay.ams.api.model.amount import Amount
from com.alipay.ams.api.model.buyer import Buyer
from com.alipay.ams.api.model.goods import Goods
from com.alipay.ams.api.model.order import Order
from com.alipay.ams.api.model.product_code_type import ProductCodeType
from com.alipay.ams.api.request.notify.alipay_capture_result_notify import AlipayCaptureResultNotify
from com.alipay.ams.api.request.notify.alipay_pay_result_notify import AlipayPayResultNotify
from com.alipay.ams.api.request.pay.alipay_create_session_request import AlipayCreateSessionRequest
from com.alipay.ams.api.request.pay.alipay_pay_query_request import AlipayPayQueryRequest
from com.alipay.ams.api.tools.webhook_tool import check_signature

'''
replace with your client id
find your client id here: https://dashboard.antom.com/global-payments/developers/quickStart
'''
CLIENT_ID = "your_client_id"

'''
replace with your antom public key (used to verify signature)
find your antom public key here: https://dashboard.antom.com/global-payments/developers/quickStart
'''
ANTOM_PUBLIC_KEY = "antom_public_key"

'''
replace with your private key (used to sign)
please ensure the secure storage of your private key to prevent leakage
'''
MERCHANT_PRIVATE_KEY = "your_private_key"


# Schema 定义
class PaymentSessionRequest(Schema):
    amountValue: float
    currency: str


class PaymentInquiryRequest(Schema):
    paymentRequestId: str


class PaymentResponse(Schema):
    success: bool
    payment_request_id: str
    data: Dict[str, Any] = None
    error: str = None


class NotifyResult(Schema):
    resultCode: str
    resultMessage: str
    resultStatus: str


class NotifyResponse(Schema):
    result: NotifyResult


@api_controller("/payment", tags=["支付"])
class AntomPaymentController:
    def __init__(self):
        self.default_alipay_client = DefaultAlipayClient("https://open-sea-global.alipay.com", CLIENT_ID,
                                                    MERCHANT_PRIVATE_KEY, ANTOM_PUBLIC_KEY)

    @route.post("/createSession", response=PaymentResponse)
    def create_payment_session(self, data: PaymentSessionRequest):
        alipay_create_session_request = AlipayCreateSessionRequest()
        alipay_create_session_request.product_code = ProductCodeType.CASHIER_PAYMENT
        alipay_create_session_request.product_scene = "CHECKOUT_PAYMENT"

        # convert amount unit(in practice, amount should be calculated on your serverside)
        # For details, please refer to: https://docs.antom.com/ac/ref/cc
        try:
            # Convert amount to sub-units (e.g., cents for USD)
            # This is a simplified conversion - in production, use proper currency handling
            money_sub_units = int(data.amountValue * 100)  # Assuming 2 decimal places for most currencies
        except Exception as e:
            raise HttpError(400, f"Invalid amount: {str(e)}")

        # set amount
        amount = Amount(data.currency, money_sub_units)
        alipay_create_session_request.payment_amount = amount

        # replace with your paymentRequestId
        payment_request_id = str(uuid.uuid4())
        alipay_create_session_request.payment_request_id = payment_request_id

        # set buyer info
        buyer = Buyer()
        buyer.reference_buyer_id = "your_buyer_id"

        # set goods info
        goods = Goods()
        goods.goods_brand = "Antom Brand"
        goods.goods_category = "outdoor goods/bag"
        goods.goods_name = "Classic Woman Bag"
        goods.goods_quantity = "1"
        goods.goods_sku_name = "Black"
        goods.goods_image_url = "https://mdn.alipayobjects.com/portal_pdqp4x/afts/file/A*H8M9RrxlArAAAAAAAAAAAAAAAQAAAQ"
        goods.goods_unit_amount = amount
        goods.goods_url = "https://yourGoodsUrl"
        goods.reference_goods_id = "yourGoodsId"

        # replace with your orderId
        order_id = str(uuid.uuid4())
        # set order info
        order = Order()
        order.reference_order_id = order_id
        order.order_description = "antom ckp testing order"
        order.order_amount = amount
        order.buyer = buyer
        order.goods = goods
        alipay_create_session_request.order = order

        # replace with your notify url
        # or configure your notify url here: https://dashboard.antom.com/global-payments/developers/iNotify
        alipay_create_session_request.payment_notify_url = "http://www.yourNotifyUrl.com/payment/receiveNotify"

        # replace with your redirect url
        alipay_create_session_request.payment_redirect_url = f"http://localhost:5173/index.html?paymentRequestId={payment_request_id}"

        try:
            start_time = time.time()
            print(f"paymentSession request: {alipay_create_session_request.to_ams_json()}")
            alipay_create_session_response_body = self.default_alipay_client.execute(alipay_create_session_request)
            print(f"paymentSession response: {alipay_create_session_response_body}")
            print(f"paymentSession request cost time: {time.time() - start_time}")

            return PaymentResponse(
                success=True,
                payment_request_id=payment_request_id,
                data=json.loads(alipay_create_session_response_body)
            )
        except AlipayApiException as e:
            raise HttpError(500, f"Payment session creation failed: {str(e)}")


    @route.post("/inquiryPayment", response=PaymentResponse)
    def inquery_payment(self, data: PaymentInquiryRequest):
        payment_request_id = data.paymentRequestId
        alipay_pay_query_request = AlipayPayQueryRequest()
        alipay_pay_query_request.payment_request_id = payment_request_id

        try:
            start_time = time.time()
            print(f"inquiry payment request: {alipay_pay_query_request.to_ams_json()}")
            alipay_pay_query_response_body = self.default_alipay_client.execute(alipay_pay_query_request)
            print(f"inquiry payment response: {alipay_pay_query_response_body}")
            print(f"inquiry payment request cost time: {time.time() - start_time}")

            return PaymentResponse(
                success=True,
                payment_request_id=payment_request_id,
                data=json.loads(alipay_pay_query_response_body)
            )
        except AlipayApiException as e:
            raise HttpError(500, f"Payment inquiry failed: {str(e)}")

    
    # receive notify
    @route.post("/receiveNotify", response=NotifyResponse)
    def receive_notify(self, request: HttpRequest):
        # retrieve the required parameters from http request
        notify_body = request.body.decode('utf-8')
        request_uri = request.path
        request_method = request.method

        # retrieve the required parameters from request header
        request_time = request.headers.get("request-time")
        client_id = request.headers.get("client-id")
        signature = request.headers.get("signature")

        try:
            # verify the signature of notification
            verify_result = check_signature(request_method, request_uri, client_id, request_time, str(notify_body),
                                            signature,
                                            ANTOM_PUBLIC_KEY)
            if not verify_result:
                raise Exception("Invalid notify signature")

            # deserialize the notification body
            notify = json.loads(notify_body)
            if notify['notifyType'] == "PAYMENT_RESULT":
                alipay_pay_result_notify = AlipayPayResultNotify(notify_body)
                if alipay_pay_result_notify.result.result_code == "SUCCESS":
                    # handle your own business logic.
                    # e.g. The relationship between payment information and users is kept in the database.
                    print(f"receive payment notify: {notify_body}")
                    return NotifyResponse(
                        result=NotifyResult(
                            resultCode="SUCCESS",
                            resultMessage="success.",
                            resultStatus="S"
                        )
                    )
            if notify['notifyType'] == "CAPTURE_RESULT":
                alipay_capture_result_notify = AlipayCaptureResultNotify(notify_body)
                if alipay_capture_result_notify.result.result_code == "SUCCESS":
                    # handle your own business logic.
                    print(f"receive capture notify: {notify_body}")
                    return NotifyResponse(
                        result=NotifyResult(
                            resultCode="SUCCESS",
                            resultMessage="success.",
                            resultStatus="S"
                        )
                    )

        except Exception as e:
            print(str(e))
            return NotifyResponse(
                result=NotifyResult(
                    resultCode="FAIL",
                    resultMessage="fail.",
                    resultStatus="F"
                )
            )

        return NotifyResponse(
            result=NotifyResult(
                resultCode="SYSTEM_ERROR",
                resultMessage="system error.",
                resultStatus="F"
            )
        )

    