# Django 配置
SECRET_KEY=your-secret-key-here

# 阿里云百炼大模型 API KEY
ALIYUN_BAILIAN_API_KEY=your-aliyun-api-key

# Antom 环境切换配置
# 设置为 'sandbox' 使用沙盒环境，设置为 'production' 使用生产环境
ANTOM_ENVIRONMENT=sandbox

# ===== Antom 沙盒环境配置 =====
# 用于开发和测试
ANTOM_SANDBOX_CLIENT_ID=your_sandbox_client_id
ANTOM_SANDBOX_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----
your_sandbox_public_key_content_here
-----END PUBLIC KEY-----
ANTOM_SANDBOX_MERCHANT_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----
your_sandbox_private_key_content_here
-----END PRIVATE KEY-----
ANTOM_SANDBOX_GATEWAY_URL=https://open-sea-global.alipay.com
ANTOM_SANDBOX_NOTIFY_URL=http://localhost:8000/api/payment/receiveNotify
ANTOM_SANDBOX_REDIRECT_URL=http://localhost:5173/payment/result

# ===== Antom 生产环境配置 =====
# 用于正式环境
ANTOM_PRODUCTION_CLIENT_ID=your_production_client_id
ANTOM_PRODUCTION_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----
your_production_public_key_content_here
-----END PUBLIC KEY-----
ANTOM_PRODUCTION_MERCHANT_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----
your_production_private_key_content_here
-----END PRIVATE KEY-----
ANTOM_PRODUCTION_GATEWAY_URL=https://open-global.alipay.com
ANTOM_PRODUCTION_NOTIFY_URL=https://yourdomain.com/api/payment/receiveNotify
ANTOM_PRODUCTION_REDIRECT_URL=https://yourdomain.com/payment/result

# 注意事项：
# 1. 复制此文件为 secrets.env 并填入真实的配置信息
# 2. 不要将包含真实密钥的 secrets.env 文件提交到版本控制系统
# 3. 沙盒环境用于开发测试，生产环境用于正式运营
# 4. 公钥和私钥需要包含完整的 BEGIN/END 标记
# 5. 回调URL需要是可以从外网访问的地址（生产环境）
