# Antom 环境配置指南

## 概述

本系统支持 Antom 支付的沙盒环境和生产环境一键切换，通过环境变量 `ANTOM_ENVIRONMENT` 来控制。

## 环境类型

### 沙盒环境 (Sandbox)
- **用途**: 开发和测试
- **特点**: 不会产生真实的资金流动
- **设置**: `ANTOM_ENVIRONMENT=sandbox`

### 生产环境 (Production)  
- **用途**: 正式运营
- **特点**: 真实的支付交易
- **设置**: `ANTOM_ENVIRONMENT=production`

## 配置步骤

### 1. 复制配置文件
```bash
cp secrets.env.example secrets.env
```

### 2. 配置环境变量

在 `secrets.env` 文件中设置：

```bash
# 选择环境：sandbox 或 production
ANTOM_ENVIRONMENT=sandbox

# 沙盒环境配置
ANTOM_SANDBOX_CLIENT_ID=your_sandbox_client_id
ANTOM_SANDBOX_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----
your_sandbox_public_key_here
-----END PUBLIC KEY-----
ANTOM_SANDBOX_MERCHANT_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----
your_sandbox_private_key_here
-----END PRIVATE KEY-----
ANTOM_SANDBOX_GATEWAY_URL=https://open-sea-global.alipay.com
ANTOM_SANDBOX_NOTIFY_URL=http://localhost:8000/api/payment/receiveNotify
ANTOM_SANDBOX_REDIRECT_URL=http://localhost:5173/payment/result

# 生产环境配置
ANTOM_PRODUCTION_CLIENT_ID=your_production_client_id
ANTOM_PRODUCTION_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----
your_production_public_key_here
-----END PUBLIC KEY-----
ANTOM_PRODUCTION_MERCHANT_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----
your_production_private_key_here
-----END PRIVATE KEY-----
ANTOM_PRODUCTION_GATEWAY_URL=https://open-global.alipay.com
ANTOM_PRODUCTION_NOTIFY_URL=https://yourdomain.com/api/payment/receiveNotify
ANTOM_PRODUCTION_REDIRECT_URL=https://yourdomain.com/payment/result
```

### 3. 验证配置

使用管理命令检查配置：

```bash
python manage.py check_antom_config
```

显示详细信息（包含部分密钥信息用于调试）：

```bash
python manage.py check_antom_config --show-keys
```

## 环境切换

### 切换到沙盒环境
```bash
# 在 secrets.env 中设置
ANTOM_ENVIRONMENT=sandbox
```

### 切换到生产环境
```bash
# 在 secrets.env 中设置
ANTOM_ENVIRONMENT=production
```

### 重启应用
修改环境变量后需要重启 Django 应用：
```bash
# 重启开发服务器
python manage.py runserver

# 或重启生产服务器
# 具体命令取决于你的部署方式
```

## API 端点

### 获取当前配置信息
```http
GET /api/payment/config
```

返回示例：
```json
{
    "environment": "sandbox",
    "gateway_url": "https://open-sea-global.alipay.com",
    "client_id_preview": "12345678...",
    "notify_url": "http://localhost:8000/api/payment/receiveNotify",
    "redirect_url": "http://localhost:5173/payment/result",
    "supported_currencies": ["HKD", "USD", "CNY", "EUR", "GBP"],
    "supported_tiers": ["free", "premium", "plus"]
}
```

## 重要注意事项

### 沙盒环境
1. **测试数据**: 所有交易都是模拟的，不会产生真实费用
2. **回调URL**: 可以使用 localhost 进行本地测试
3. **测试卡号**: 使用 Antom 提供的测试卡号进行测试

### 生产环境
1. **真实交易**: 所有支付都是真实的，会产生实际费用
2. **HTTPS要求**: 回调URL和重定向URL必须使用HTTPS
3. **域名要求**: 不能使用localhost，必须使用真实域名
4. **安全性**: 确保私钥安全存储，不要泄露

### 密钥管理
1. **分离存储**: 沙盒和生产环境的密钥要分开存储
2. **权限控制**: 限制对生产环境密钥的访问权限
3. **定期轮换**: 定期更换生产环境的密钥
4. **备份**: 安全备份密钥信息

## 故障排除

### 常见问题

1. **配置未生效**
   - 检查环境变量是否正确设置
   - 重启应用服务器
   - 运行 `check_antom_config` 命令验证

2. **回调失败**
   - 确保回调URL可以从外网访问（生产环境）
   - 检查防火墙和网络配置
   - 验证签名配置是否正确

3. **支付失败**
   - 检查当前环境是否正确
   - 验证密钥配置
   - 查看应用日志获取详细错误信息

### 调试命令

```bash
# 检查配置
python manage.py check_antom_config --show-keys

# 查看当前环境
python manage.py shell -c "from django.conf import settings; print(f'Environment: {settings.ANTOM_ENVIRONMENT}')"

# 测试支付创建
curl -X GET http://localhost:8000/api/payment/config
```

## 部署建议

### 开发环境
- 使用沙盒环境
- 回调URL可以使用ngrok等工具暴露本地服务

### 测试环境
- 使用沙盒环境
- 使用真实域名但指向测试服务器

### 生产环境
- 使用生产环境配置
- 确保所有URL使用HTTPS
- 实施适当的监控和日志记录

通过这种配置方式，你可以轻松地在不同环境之间切换，确保开发测试的便利性和生产环境的安全性。
